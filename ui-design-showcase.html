<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监督App - UI设计稿展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .showcase-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .showcase-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .showcase-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .phone-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .phone-mockup {
            width: 320px;
            height: 640px;
            background: #1a1a1a;
            border-radius: 30px;
            padding: 20px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 0 auto;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .screen-content {
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 通用样式 */
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .nav-header {
            height: 60px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a1a;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            background: #f8fafc;
            overflow-y: auto;
        }

        /* 登录页面 */
        .login-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 30px;
            color: white;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .login-subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 40px;
            text-align: center;
        }

        .role-selector {
            width: 100%;
            margin-bottom: 30px;
        }

        .role-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .role-option.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.8);
        }

        .role-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .role-name {
            font-size: 16px;
            font-weight: 600;
        }

        .login-btn {
            width: 100%;
            background: white;
            color: #667eea;
            border: none;
            border-radius: 16px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* 仪表板 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #6366f1;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 15px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .action-btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #6366f1;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .phone-label {
            text-align: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-top: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* 任务列表样式 */
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .task-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #6366f1;
        }

        .task-icon {
            font-size: 20px;
            margin-right: 12px;
        }

        .task-content {
            flex: 1;
        }

        .task-title {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .task-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .task-status.completed {
            background: #dcfce7;
            color: #16a34a;
        }

        .task-status.pending {
            background: #fef3c7;
            color: #d97706;
        }

        /* 日记编辑器样式 */
        .diary-editor {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .date-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .date-display {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .mood-selector {
            display: flex;
            gap: 8px;
        }

        .mood-emoji {
            font-size: 24px;
            cursor: pointer;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .mood-emoji.active {
            opacity: 1;
            transform: scale(1.2);
        }

        .diary-content {
            margin-bottom: 20px;
        }

        .diary-textarea {
            width: 100%;
            min-height: 200px;
            border: none;
            outline: none;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            resize: none;
            font-family: inherit;
        }

        .diary-stats {
            display: flex;
            justify-content: space-between;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #6366f1;
        }

        /* 表单样式 */
        .form-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select, .form-textarea {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .urgency-selector {
            background: white;
            border-radius: 12px;
            padding: 16px;
            border: 2px solid #e5e7eb;
        }

        .urgency-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .urgency-options {
            display: flex;
            justify-content: space-between;
        }

        .urgency-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .urgency-option.active {
            background: #f0f9ff;
        }

        .urgency-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-bottom: 4px;
        }

        .urgency-dot.low {
            background: #10b981;
        }

        .urgency-dot.medium {
            background: #f59e0b;
        }

        .urgency-dot.high {
            background: #ef4444;
        }

        /* 按钮样式 */
        .save-btn, .submit-btn {
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .save-btn:hover, .submit-btn:hover {
            background: #4f46e5;
        }

        /* 监督人特有样式 */
        .supervisor-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .action-btn.supervisor {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .activity-icon {
            font-size: 20px;
            margin-right: 12px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .activity-desc {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 11px;
            color: #9ca3af;
        }

        .review-btn, .view-btn {
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
        }

        .view-btn {
            background: #64748b;
        }

        /* 审批卡片样式 */
        .approval-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .approval-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .applicant-info {
            display: flex;
            align-items: center;
        }

        .applicant-avatar {
            width: 40px;
            height: 40px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: 12px;
        }

        .applicant-name {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .apply-time {
            font-size: 12px;
            color: #64748b;
        }

        .urgency-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }

        .urgency-badge.medium {
            background: #fef3c7;
            color: #d97706;
        }

        .approval-content {
            margin-bottom: 20px;
        }

        .event-type {
            font-size: 14px;
            color: #6366f1;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .event-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 12px;
        }

        .event-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .detail-item {
            display: flex;
        }

        .detail-label {
            font-size: 14px;
            color: #64748b;
            min-width: 80px;
        }

        .detail-value {
            font-size: 14px;
            color: #1a1a1a;
            font-weight: 500;
        }

        .approval-actions {
            display: flex;
            gap: 12px;
        }

        .reject-btn, .approve-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .reject-btn {
            background: #fee2e2;
            color: #dc2626;
        }

        .approve-btn {
            background: #dcfce7;
            color: #16a34a;
        }

        /* 历史记录样式 */
        .approval-history {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .history-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
        }

        .history-item.approved {
            background: #f0fdf4;
        }

        .history-item.rejected {
            background: #fef2f2;
        }

        .history-icon {
            font-size: 16px;
            margin-right: 12px;
        }

        .history-title {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
        }

        .history-time {
            font-size: 12px;
            color: #64748b;
        }

        /* 计划管理样式 */
        .add-btn {
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .plan-calendar {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-nav {
            background: none;
            border: none;
            font-size: 18px;
            color: #6366f1;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .calendar-nav:hover {
            background: #f0f9ff;
        }

        .calendar-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .calendar-grid {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

        .calendar-day {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-day.today {
            background: #6366f1;
            color: white;
        }

        .calendar-day:hover:not(.today) {
            background: #f0f9ff;
            color: #6366f1;
        }

        .plan-list {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .plan-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            background: #f8fafc;
            transition: all 0.3s ease;
        }

        .plan-item.completed {
            background: #f0fdf4;
            opacity: 0.8;
        }

        .plan-checkbox {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .plan-item.completed .plan-checkbox {
            background: #16a34a;
            color: white;
        }

        .plan-item:not(.completed) .plan-checkbox {
            border: 2px solid #d1d5db;
            color: #d1d5db;
        }

        .plan-content {
            flex: 1;
        }

        .plan-title {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .plan-item.completed .plan-title {
            text-decoration: line-through;
            color: #64748b;
        }

        .plan-time {
            font-size: 12px;
            color: #64748b;
        }

        .plan-priority {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 12px;
        }

        .plan-priority.high {
            background: #ef4444;
        }

        .plan-priority.medium {
            background: #f59e0b;
        }

        .plan-priority.low {
            background: #10b981;
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <div class="showcase-header">
            <h1>监督App UI设计稿</h1>
            <p>创新简约的移动端界面设计</p>
        </div>

        <div class="phone-grid">
            <!-- 登录页面 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="login-screen">
                        <div class="login-logo">👁️</div>
                        <h1 class="login-title">监督助手</h1>
                        <p class="login-subtitle">让每一天都充满意义<br>选择您的身份开始使用</p>

                        <div class="role-selector">
                            <div class="role-option active">
                                <div class="role-icon">👤</div>
                                <div class="role-name">被监督人</div>
                            </div>
                            <div class="role-option">
                                <div class="role-icon">👨‍💼</div>
                                <div class="role-name">监督人</div>
                            </div>
                        </div>

                        <button class="login-btn">开始使用</button>
                    </div>
                </div>
                <div class="phone-label">登录页面</div>
            </div>

            <!-- 被监督人仪表板 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">今日概览</div>
                            <div style="font-size: 20px;">🔔</div>
                        </div>

                        <div class="content-area">
                            <div class="dashboard-stats">
                                <div class="stat-card">
                                    <div class="stat-number">7</div>
                                    <div class="stat-label">连续打卡天数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">¥50</div>
                                    <div class="stat-label">当前余额</div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="section-title">快速操作</div>
                                <div class="action-grid">
                                    <button class="action-btn">📝 写日记</button>
                                    <button class="action-btn">📋 查看计划</button>
                                    <button class="action-btn">📤 申请事件</button>
                                    <button class="action-btn">📊 查看统计</button>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="section-title">今日任务</div>
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">✅</div>
                                        <div class="task-content">
                                            <div class="task-title">写今日日记</div>
                                            <div class="task-status completed">已完成</div>
                                        </div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">⏰</div>
                                        <div class="task-content">
                                            <div class="task-title">完成学习计划</div>
                                            <div class="task-status pending">进行中</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📝</div>
                                <div class="nav-label">日记</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">计划</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">被监督人 - 首页</div>
            </div>

            <!-- 日记页面 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">今日日记</div>
                            <button class="save-btn">保存</button>
                        </div>

                        <div class="content-area">
                            <div class="diary-editor">
                                <div class="date-header">
                                    <div class="date-display">2024年1月15日 星期一</div>
                                    <div class="mood-selector">
                                        <span class="mood-emoji active">😊</span>
                                        <span class="mood-emoji">😐</span>
                                        <span class="mood-emoji">😔</span>
                                    </div>
                                </div>

                                <div class="diary-content">
                                    <textarea class="diary-textarea" placeholder="记录今天发生的事情...">今天完成了学习计划，感觉很充实。上午专注学习了2小时，下午完成了运动目标。</textarea>
                                </div>

                                <div class="diary-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">字数</span>
                                        <span class="stat-value">45</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">写作时间</span>
                                        <span class="stat-value">5分钟</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item active">
                                <div class="nav-icon">📝</div>
                                <div class="nav-label">日记</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">计划</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">日记编写页面</div>
            </div>

            <!-- 事件申请页面 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">申请事件</div>
                            <button class="submit-btn">提交</button>
                        </div>

                        <div class="content-area">
                            <div class="form-container">
                                <div class="form-group">
                                    <label class="form-label">事件类型</label>
                                    <select class="form-select">
                                        <option>外出活动</option>
                                        <option>购买物品</option>
                                        <option>娱乐活动</option>
                                        <option>其他</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">事件描述</label>
                                    <textarea class="form-textarea" placeholder="详细描述您要做的事情...">想要和朋友一起去看电影，预计时间2小时</textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">预计时间</label>
                                    <input type="datetime-local" class="form-input" value="2024-01-15T19:00">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">预计费用</label>
                                    <input type="number" class="form-input" placeholder="0" value="80">
                                </div>

                                <div class="urgency-selector">
                                    <div class="urgency-title">紧急程度</div>
                                    <div class="urgency-options">
                                        <div class="urgency-option">
                                            <div class="urgency-dot low"></div>
                                            <span>低</span>
                                        </div>
                                        <div class="urgency-option active">
                                            <div class="urgency-dot medium"></div>
                                            <span>中</span>
                                        </div>
                                        <div class="urgency-option">
                                            <div class="urgency-dot high"></div>
                                            <span>高</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📝</div>
                                <div class="nav-label">日记</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">计划</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">事件申请页面</div>
            </div>

            <!-- 监督人仪表板 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">监督中心</div>
                            <div style="font-size: 20px;">⚙️</div>
                        </div>

                        <div class="content-area">
                            <div class="supervisor-stats">
                                <div class="stat-card">
                                    <div class="stat-number">3</div>
                                    <div class="stat-label">待审批事件</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">15</div>
                                    <div class="stat-label">监督天数</div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="section-title">监督功能</div>
                                <div class="action-grid">
                                    <button class="action-btn supervisor">📋 审批事件</button>
                                    <button class="action-btn supervisor">📝 查看日记</button>
                                    <button class="action-btn supervisor">📅 发布计划</button>
                                    <button class="action-btn supervisor">💰 扣款管理</button>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="section-title">最新动态</div>
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon">📤</div>
                                        <div class="activity-content">
                                            <div class="activity-title">新的事件申请</div>
                                            <div class="activity-desc">想要去看电影</div>
                                            <div class="activity-time">5分钟前</div>
                                        </div>
                                        <button class="review-btn">审批</button>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon">📝</div>
                                        <div class="activity-content">
                                            <div class="activity-title">日记已提交</div>
                                            <div class="activity-desc">今日学习总结</div>
                                            <div class="activity-time">2小时前</div>
                                        </div>
                                        <button class="view-btn">查看</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">审批</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📊</div>
                                <div class="nav-label">统计</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">监督人 - 首页</div>
            </div>

            <!-- 事件审批页面 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">事件审批</div>
                            <div style="font-size: 20px;">📋</div>
                        </div>

                        <div class="content-area">
                            <div class="approval-card">
                                <div class="approval-header">
                                    <div class="applicant-info">
                                        <div class="applicant-avatar">👤</div>
                                        <div class="applicant-details">
                                            <div class="applicant-name">张三</div>
                                            <div class="apply-time">2024-01-15 14:30</div>
                                        </div>
                                    </div>
                                    <div class="urgency-badge medium">中等</div>
                                </div>

                                <div class="approval-content">
                                    <div class="event-type">🎬 娱乐活动</div>
                                    <div class="event-title">想要和朋友一起去看电影</div>
                                    <div class="event-details">
                                        <div class="detail-item">
                                            <span class="detail-label">预计时间：</span>
                                            <span class="detail-value">今晚19:00</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">预计费用：</span>
                                            <span class="detail-value">¥80</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="approval-actions">
                                    <button class="reject-btn">拒绝</button>
                                    <button class="approve-btn">同意</button>
                                </div>
                            </div>

                            <div class="approval-history">
                                <div class="section-title">历史记录</div>
                                <div class="history-list">
                                    <div class="history-item approved">
                                        <div class="history-icon">✅</div>
                                        <div class="history-content">
                                            <div class="history-title">购买学习用品</div>
                                            <div class="history-time">昨天 15:30</div>
                                        </div>
                                    </div>
                                    <div class="history-item rejected">
                                        <div class="history-icon">❌</div>
                                        <div class="history-content">
                                            <div class="history-title">外出聚餐</div>
                                            <div class="history-time">前天 20:15</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item active">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">审批</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📊</div>
                                <div class="nav-label">统计</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">事件审批页面</div>
            </div>

            <!-- 计划管理页面 -->
            <div class="phone-mockup">
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span>100%</span>
                        </div>

                        <div class="nav-header">
                            <div class="nav-title">我的计划</div>
                            <button class="add-btn">+</button>
                        </div>

                        <div class="content-area">
                            <div class="plan-calendar">
                                <div class="calendar-header">
                                    <button class="calendar-nav">‹</button>
                                    <div class="calendar-title">2024年1月</div>
                                    <button class="calendar-nav">›</button>
                                </div>
                                <div class="calendar-grid">
                                    <div class="calendar-day">14</div>
                                    <div class="calendar-day today">15</div>
                                    <div class="calendar-day">16</div>
                                    <div class="calendar-day">17</div>
                                    <div class="calendar-day">18</div>
                                </div>
                            </div>

                            <div class="plan-list">
                                <div class="section-title">今日计划</div>
                                <div class="plan-item completed">
                                    <div class="plan-checkbox">✓</div>
                                    <div class="plan-content">
                                        <div class="plan-title">完成数学作业</div>
                                        <div class="plan-time">09:00 - 11:00</div>
                                    </div>
                                    <div class="plan-priority high"></div>
                                </div>
                                <div class="plan-item">
                                    <div class="plan-checkbox">○</div>
                                    <div class="plan-content">
                                        <div class="plan-title">阅读30分钟</div>
                                        <div class="plan-time">14:00 - 14:30</div>
                                    </div>
                                    <div class="plan-priority medium"></div>
                                </div>
                                <div class="plan-item">
                                    <div class="plan-checkbox">○</div>
                                    <div class="plan-content">
                                        <div class="plan-title">运动锻炼</div>
                                        <div class="plan-time">18:00 - 19:00</div>
                                    </div>
                                    <div class="plan-priority low"></div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-nav">
                            <div class="nav-item">
                                <div class="nav-icon">🏠</div>
                                <div class="nav-label">首页</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">📝</div>
                                <div class="nav-label">日记</div>
                            </div>
                            <div class="nav-item active">
                                <div class="nav-icon">📋</div>
                                <div class="nav-label">计划</div>
                            </div>
                            <div class="nav-item">
                                <div class="nav-icon">👤</div>
                                <div class="nav-label">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="phone-label">计划管理页面</div>
            </div>
        </div>
    </div>
</body>
</html>
